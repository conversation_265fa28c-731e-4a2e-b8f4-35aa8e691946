// 環境配置檔案
window.AppConfig = {
    // 開發環境配置
    development: {
        API_BASE: "http://localhost:8080",
        DEBUG: true
    },

    // 生產環境配置
    production: {
        API_BASE: "https://16bd13b979ec.ngrok-free.app",
        DEBUG: false
    },

    // 測試環境配置
    staging: {
        API_BASE: "https://16bd13b979ec.ngrok-free.app",
        DEBUG: true
    },

    // 獲取當前環境配置
    getCurrentConfig() {
        const hostname = window.location.hostname;

        // 判斷當前環境
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            return this.development;
        } else if (hostname.includes('staging') || hostname.includes('test')) {
            return this.staging;
        } else {
            return this.production;
        }
    }
};

// 導出當前環境的配置
window.CONFIG = window.AppConfig.getCurrentConfig();